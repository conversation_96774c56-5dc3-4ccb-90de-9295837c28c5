:root {
  --bs-primary: #1559b4;
  --bs-body-bg: #fff;
  --bs-body-color: var(--bs-gray-800);
}
[data-bs-theme="dark"] {
  --bs-primary: #1559b4;
  --bs-body-bg: #111827;
  --bs-body-color: var(--bs-gray-200);
  --bs-text-primary: #4a92e6;
}

body {
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
  font-family: 'Inter', sans-serif;
  font-size: .875rem;
  overflow-x: hidden;
}

.df-header {
  margin: 0 auto;
}

.df-logo {
  text-decoration: none;
}

.df-logo__text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--bs-body-color);
}

.df-logo__image {
  width: auto;
  height: 2rem;
}

.df-nav-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 3rem;
}

.df-nav-item {
  color: var(--bs-body-color) !important;
  text-decoration: none;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.5rem;
}

.df-nav-item:hover {
  color: var(--bs-primary) !important;
}

.df-mobile-menu-btn {
  border: none;
  background: transparent;
}

.df-mobile-menu-btn:focus {
  box-shadow: none;
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

[data-bs-theme="dark"] .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.df-btn--outline-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  padding: .5rem .75rem;
  border-radius: var(--bs-border-radius-pill);
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border: 1px solid var(--bs-primary);
  cursor: pointer;
  color: var(--bs-primary);
  text-decoration: none;
  background-color: transparent;
  transition: .25s;
}

.df-btn--outline-primary:hover {
  color: var(--bs-white);
  background-color: var(--bs-primary);
}
[data-bs-theme="dark"] .df-btn--outline-primary {
  background-color: #4a92e61a;
  color: var(--bs-text-primary);
}
[data-bs-theme="dark"] .df-btn--outline-primary:hover {
  color: var(--bs-primary);
}
.icon {
  width: 1rem;
  height: 1rem;
}

/* df-intro */
.df-intro {
  position: relative;
  padding: 16rem 2rem;
  isolation: isolate;
}

.df-intro-main {
  position: absolute;
  left: 0;
  right: 0;
  top: 2.5rem;
  z-index: -10;
  display: flex;
  justify-content: center;
  overflow: hidden;
  filter: blur(64px);
}

.df-intro-main__image {
  aspect-ratio: 1108 / 632;
  width: 69.25rem;
  flex: none;
  background-image: linear-gradient(to right, #1559b4, #2563eb);
  opacity: .2;
}

.df-intro-content {
  margin: 0 auto;
  max-width: 56rem;
  text-align: center;
}

.df-intro-head {
  font-size: 3.5rem;
  font-weight: 600;
  letter-spacing: -.025rem;
  line-height: 1;
}

.df-intro-sub {
  font-size: 1rem;
  font-weight: 400;
}

.df-btn--primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  padding: .75rem .95rem;
  border-radius: var(--bs-border-radius-pill);
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border: 1px solid var(--bs-primary);
  cursor: pointer;
  color: var(--bs-white);
  text-decoration: none;
  background-color: var(--bs-primary);
  transition: .25s;
}
[data-bs-theme="dark"] .df-btn--primary {
  background-color: var(--bs-white);
  border-color: transparent;
  color: var(--bs-primary)
}
[data-bs-theme="dark"] .df-btn--primary:hover {
  background-color: var(--bs-white);
  border-color: transparent;
  color: var(--bs-primary)
}
.df-btn--primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  color: var(--bs-white);
}
.df-text-primary {
  color: var(--bs-primary);
  opacity: 1;
}
[data-bs-theme="dark"] .df-text-primary {
  color: var(--bs-text-primary);
}
.df-text-button{
  color:var(--bs-body-color);
  font-weight: 600;
  text-decoration: none;
}
/* df-highlights */
.df-highlights{
  margin-top: 10rem;
}
.df-highlights-head{
  font-size:2.25rem;
  line-height: 2.5rem;
  color: var(--bs-body-color)

}

.df-highlights-sub{
  font-size: 1rem;
  margin-top: 1rem;
}


.df-highlights-image{
  width: 100%;
  height: auto;
  border-radius: var(--bs-border-radius-2xl);
}
.mt-8{
  margin-top: 8rem;
}
/* Mobile Responsive Styles */
@media (max-width: 991.98px) {
  /* Header responsive */
  .df-nav-list {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    text-align: center;
    margin-top: 1rem;
  }

  .df-header-cta {
    margin-top: 1rem;
    text-align: center;
  }

  .df-btn--outline-primary {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 768px) {
  /* Intro section responsive */
  .df-intro {
    padding: 6rem 1rem 4rem;
  }

  .df-intro-head {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .df-intro-sub {
    font-size: 0.9rem;
    margin-top: 1rem;
    padding: 0 1rem;
  }

  .df-intro-main__image {
    width: 100%;
    max-width: 40rem;
  }

  /* Buttons responsive */
  .d-flex.align-items-center.justify-content-center {
    flex-direction: column;
    gap: 1rem !important;
  }

  .df-btn--primary,
  .df-btn--outline-primary {
    width: 100%;
    max-width: 280px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
  }

  .df-text-button {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  /* Extra small screens */
  .df-intro {
    padding: 12rem 0.5rem;
  }

  .df-intro-head {
    font-size: 2rem;
  }

  .df-intro-sub {
    font-size: 0.85rem;
  }

  .df-nav-list {
    gap: 0.75rem;
  }

  .df-nav-item {
    font-size: 0.8rem;
  }

  .df-logo__text {
    font-size: 1.1rem;
  }
  .mt-8{
    margin-top: 4rem;
  }
  .df-highlights-head{
    font-size: 1.5rem;
  }
}

/* Tablet responsive */
@media (min-width: 769px) and (max-width: 1024px) {
  .df-nav-list {
    gap: 2rem;
  }

  .df-intro {
    padding: 8rem 1.5rem;
  }

  .df-intro-head {
    font-size: 3rem;
  }

  .df-intro-main__image {
    width: 60rem;
  }
}

/* Touch improvements for mobile */
@media (max-width: 768px) {
  /* Improve touch targets */
  .df-nav-item {
    padding: 0.75rem 1rem;
    display: block;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
  }

  .df-nav-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
  }

  /* Better spacing for mobile */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Ensure buttons are easily tappable */
  .df-btn--primary,
  .df-btn--outline-primary {
    min-height: 44px;
    touch-action: manipulation;
  }
}